// src/store/userStore.ts
import { create } from 'zustand';
import { useAuthStore } from './authStore';

// User type definition
export interface User {
  id: string;
  employeeCode: string;
  name: string;
  fullName: string;
  mobile: string;
  status: 'Approved' | 'Pending';
  roles: {
    dashboard: {
      view: boolean;
      edit: boolean;
    };
    users: {
      view: boolean;
      edit: boolean;
    };
    cards: {
      view: boolean;
      edit: boolean;
    };
    rule: {
      view: boolean;
      edit: boolean;
    };
    applications: {
      view: boolean;
      edit: boolean;
    };
  };
}

// Mock user data
const mockUsers: User[] = [
  {
    id: '1',
    employeeCode: '5311',
    name: '<PERSON><PERSON>',
    fullName: '<PERSON><PERSON>',
    mobile: '1234567871',
    status: 'Pending',
    roles: {
      dashboard: { view: true, edit: false },
      users: { view: true, edit: false },
      cards: { view: true, edit: false },
      rule: { view: false, edit: false },
      applications: { view: true, edit: false },
    },
  },
  {
    id: '2',
    employeeCode: '5311',
    name: '<PERSON><PERSON><PERSON>',
    fullName: '<PERSON><PERSON><PERSON>',
    mobile: '1234567871',
    status: 'Approved',
    roles: {
      dashboard: { view: true, edit: true },
      users: { view: true, edit: true },
      cards: { view: true, edit: true },
      rule: { view: true, edit: true },
      applications: { view: true, edit: true },
    },
  },
  {
    id: '3',
    employeeCode: '5311',
    name: 'Rahul Kumar',
    fullName: 'Rahul Kumar',
    mobile: '1234567871',
    status: 'Pending',
    roles: {
      dashboard: { view: true, edit: false },
      users: { view: false, edit: false },
      cards: { view: true, edit: false },
      rule: { view: false, edit: false },
      applications: { view: true, edit: false },
    },
  },
  {
    id: '4',
    employeeCode: '5311',
    name: 'Suraj Vishwas',
    fullName: 'Suraj Vishwas',
    mobile: '1234567871',
    status: 'Pending',
    roles: {
      dashboard: { view: true, edit: false },
      users: { view: false, edit: false },
      cards: { view: true, edit: false },
      rule: { view: true, edit: false },
      applications: { view: true, edit: false },
    },
  },
  {
    id: '5',
    employeeCode: '5311',
    name: 'Pratik Kumar',
    fullName: 'Pratik Kumar',
    mobile: '1234567871',
    status: 'Approved',
    roles: {
      dashboard: { view: true, edit: true },
      users: { view: true, edit: false },
      cards: { view: true, edit: false },
      rule: { view: true, edit: false },
      applications: { view: true, edit: false },
    },
  },
];

// Store type
interface UserState {
  users: User[];
  isLoading: boolean;
  error: string | null;
  currentUser: User | null;
  
  // Simulated API actions
  fetchUsers: () => Promise<void>;
  fetchUserById: (id: string) => Promise<void>;
  createUser: (userData: Omit<User, 'id'>) => Promise<void>;
  updateUser: (id: string, userData: Partial<User>) => Promise<void>;
  deleteUser: (id: string) => Promise<void>;
}

// Create store with mock data and simulated API
export const useUserStore = create<UserState>((set, get) => ({
  users: [],
  isLoading: false,
  error: null,
  currentUser: null,
  
  // Fetch all users
  fetchUsers: async () => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));
      set({ users: mockUsers, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: 'Failed to fetch users. Please try again.' 
      });
    }
  },
  
  // Fetch user by ID
  fetchUserById: async (id: string) => {
    set({ isLoading: true, error: null, currentUser: null });
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 600));
      const user = mockUsers.find(u => u.id === id);
      
      if (!user) {
        throw new Error('User not found');
      }
      
      set({ currentUser: user, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch user' 
      });
    }
  },
  
  // Create new user
  createUser: async (userData: Omit<User, 'id'>) => {
    set({ isLoading: true, error: null });
  
    try {
      const token = useAuthStore.getState().token;
  
      const response = await fetch('http://bank-data-pdf-light-mode-*********.ap-south-1.elb.amazonaws.com/card/create-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          employee_code: userData.employeeCode,
          full_name: userData.fullName,
          mobile: userData.mobile,
          permissions: userData.roles,
        })
      });
  
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'API Error');
      }
  
      // Optional: If API returns the created user, you can parse it
      const newUser = await response.json();
  
      // You could fetch the list again, or optimistically add:
      set((state) => ({
        users: [...state.users, { ...userData, id: newUser.id || Date.now().toString(), status: 'Pending' }],
        isLoading: false,
      }));
    } catch (error) {
      set({ 
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to create user',
      });
    }
  },
  
  // Update existing user
  updateUser: async (id: string, userData: Partial<User>) => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Find and update user in mock data
      const userIndex = mockUsers.findIndex(u => u.id === id);
      
      if (userIndex === -1) {
        throw new Error('User not found');
      }
      
      mockUsers[userIndex] = {
        ...mockUsers[userIndex],
        ...userData
      };
      
      // Update state
      set({ 
        users: [...mockUsers],
        currentUser: mockUsers[userIndex],
        isLoading: false 
      });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to update user' 
      });
    }
  },
  
  // Delete user
  deleteUser: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 700));
      
      // Filter out the user from mock data
      const updatedUsers = mockUsers.filter(u => u.id !== id);
      
      if (updatedUsers.length === mockUsers.length) {
        throw new Error('User not found');
      }
      
      // Update the mock data reference
      mockUsers.length = 0;
      mockUsers.push(...updatedUsers);
      
      // Update state
      set({ 
        users: updatedUsers,
        currentUser: null,
        isLoading: false 
      });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to delete user' 
      });
    }
  }
}));