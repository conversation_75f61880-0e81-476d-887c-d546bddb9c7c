import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { useAuthStore } from './authStore';

// Mock application data based on the UI mockups
const mockApplications = [
  {
    id: '1',
    date: '12-10-2024',
    mCode: '<PERSON><PERSON>',
    name: '<PERSON><PERSON>',
    mobile: '1234568971',
    score: 271,
    status: 'Bureau check',
    fullName: '<PERSON><PERSON>',
    employmentType: 'Salaried',
    salaryRange: '10 - 15 LPA',
    pan: '**********',
    creditScore: 720,
    pincode: '560043',
    cardApplied: 'Card Name',
    merchantCode: '511',
    currentStep: 1
  },
  {
    id: '2',
    date: '12-10-2024',
    mCode: '<PERSON><PERSON><PERSON>',
    name: '<PERSON><PERSON><PERSON>',
    mobile: '1234568971',
    score: 271,
    status: 'Card Offered',
    fullName: '<PERSON><PERSON><PERSON>',
    employmentType: 'Salaried',
    salaryRange: '15 - 20 LPA',
    pan: '**********',
    creditScore: 680,
    pincode: '560034',
    cardApplied: 'Premium Card',
    merchantCode: '622',
    currentStep: 2
  },
  {
    id: '3',
    date: '12-10-2024',
    mCode: 'Rahul Kumar',
    name: 'Rahul <PERSON>',
    mobile: '1234568971',
    score: 271,
    status: 'Card Offered',
    fullName: 'Rahul Kumar',
    employmentType: 'Self-Employed',
    salaryRange: '25 - 30 LPA',
    pan: '**********',
    creditScore: 790,
    pincode: '560001',
    cardApplied: 'Gold Card',
    merchantCode: '733',
    currentStep: 2
  },
  {
    id: '4',
    date: '12-10-2024',
    mCode: 'Suraj Vishwas',
    name: 'Suraj Vishwas',
    mobile: '1234568971',
    score: 271,
    status: 'Application Started',
    fullName: 'Suraj Vishwas',
    employmentType: 'Business Owner',
    salaryRange: '35+ LPA',
    pan: '**********',
    creditScore: 820,
    pincode: '560076',
    cardApplied: 'Platinum Card',
    merchantCode: '844',
    currentStep: 0
  },
  {
    id: '5',
    date: '12-10-2024',
    mCode: 'Pratik Kumar',
    name: 'Pratik Kumar',
    mobile: '1234568971',
    score: 271,
    status: 'Card Offered',
    fullName: 'Pratik Kumar',
    employmentType: 'Salaried',
    salaryRange: '20 - 25 LPA',
    pan: '**********',
    creditScore: 710,
    pincode: '560103',
    cardApplied: 'Silver Card',
    merchantCode: '955',
    currentStep: 2
  }
];

// Define the application store type
type ApplicationStore = {
  applications: typeof mockApplications;
  isLoading: boolean;
  error: string | null;
  fetchApplications: () => Promise<void>;
  getApplicationById: (id?: string) => typeof mockApplications[0] | undefined;
  updateApplicationStatus: (id: string, status: string, step: number) => Promise<void>;
};

// Create the Zustand store with simulated API delays
export const useApplicationStore = create<ApplicationStore>()(
  persist(
    (set, get) => ({
      applications: mockApplications,
      isLoading: false,
      error: null,
      
      // Fetch all applications with simulated API delay
      fetchApplications: async () => {
        set({ isLoading: true, error: null });
      
        try {
          const token = useAuthStore.getState().token;
      
          const response = await fetch(
            'http://bank-data-pdf-light-mode-*********.ap-south-1.elb.amazonaws.com/card/admin_leads',
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${token}`
              },
              body: JSON.stringify({
                page: 1,
                limit: 15
              })
            }
          );
      
          if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
          }
      
          const data = await response.json();
      
          // Map the backend response to your frontend format
          const mappedApplications = data.leads.map((lead: any, index: number) => ({
            id: lead.txn_id || index.toString(),
            date: lead.created_at.split(' ')[0], // Extract date from timestamp
            mCode: lead.agent_name,
            name: lead.name,
            mobile: lead.phone_no,
            score: 0, // No score in response; set default
            status: lead.card_status,
            fullName: lead.name,
            employmentType: lead.employment_type,
            salaryRange: lead.salary_range,
            pan: lead.pan_no,
            creditScore: 0, // Set 0 or compute if available
            pincode: lead.pin_code,
            cardApplied: lead.card_name,
            merchantCode: '', // No merchant code in response
            currentStep: 0 // Default step
          }));
      
          set({
            applications: mappedApplications,
            isLoading: false
          });
        } catch (error) {
          set({
            error: 'Failed to fetch applications. Please try again later.',
            isLoading: false
          });
        }
      },
      
      // Get application by ID
      getApplicationById: (id) => {
        if (!id) return undefined;
        return get().applications.find(app => app.id === id);
      },
      
      // Update application status with simulated API delay
      updateApplicationStatus: async (id, status, step) => {
        set({ isLoading: true, error: null });
        
        try {
          // Simulate API delay
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          set(state => ({
            applications: state.applications.map(app => 
              app.id === id 
                ? { ...app, status, currentStep: step } 
                : app
            ),
            isLoading: false
          }));
        } catch (error) {
          set({ 
            error: 'Failed to update application status. Please try again later.', 
            isLoading: false 
          });
        }
      }
    }),
    {
      name: 'application-store',
      partialize: (state) => ({ applications: state.applications }),
    }
  )
);