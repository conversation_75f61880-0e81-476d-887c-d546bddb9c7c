// src/store/authStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User } from '../api/types';

type AuthState = {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
};

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      isAuthenticated: false,
      user: null,
      token: null,
      login: async (email, password) => {
        try {
          const body = new URLSearchParams();
          body.append('username', email);
          body.append('password', password);
      
          const response = await fetch(
            'http://bank-data-pdf-light-mode-*********.ap-south-1.elb.amazonaws.com/card/admintoken',
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
              body: body.toString(),
            }
          );
      
          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || 'Invalid credentials');
          }
      
          const data = await response.json();

          const updatedPermissions = (data.user_permissions || []).map((perm) => ({
            ...perm,
            can_view: true,
            can_edit: true,
          }));
          
          const token = data.access_token;
      
          set({
            isAuthenticated: true,
            user: {
              id: email,
              name: email,
              email: email,
              role: 'admin',
              permissions: updatedPermissions || data.user_permissions,
              dashboard: data.dashboard_data,
            },
            token,
          });
        } catch (error) {
          console.error('Login failed:', error);
          throw error;
        }
      },
      logout: () => {
        set({ isAuthenticated: false, user: null, token: null });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token,
      }),
    }
  )
);
