import { create } from 'zustand';
import { useAuthStore } from './authStore';

// Types
interface CardMetric {
  name: string;
  total: number;
  approved: number;
}

interface ApplicationMetric {
  name: string;
  application: number;
  approved: number;
}

interface TrendPoint {
  value: number;
}

interface DashboardState {
  isLoading: boolean;
  error: string | null;
  totalApplications: number;
  totalApproved: number;
  applicationTrend: TrendPoint[];
  approvalTrend: TrendPoint[];
  cardMetrics: CardMetric[];
  applicationMetrics: ApplicationMetric[];
  timeframe: 'daily' | 'weekly' | 'monthly' | '3months';
  fetchDashboardData: () => Promise<void>;
  setTimeframe: (timeframe: 'daily' | 'weekly' | 'monthly' | '3months') => void;
}

// Mock data
const mockCardMetrics: CardMetric[] = [
  { name: 'HDFC', total: 50, approved: 30 },
  { name: 'Axis', total: 45, approved: 35 },
  { name: 'ICICI', total: 55, approved: 20 },
];

const mockDailyApplicationMetrics: ApplicationMetric[] = [
  { name: 'Sat', application: 400, approved: 230 },
  { name: 'Sun', application: 330, approved: 110 },
  { name: 'Mon', application: 310, approved: 250 },
  { name: 'Tue', application: 400, approved: 350 },
  { name: 'Wed', application: 140, approved: 230 },
  { name: 'Thu', application: 350, approved: 230 },
  { name: 'Fri', application: 400, approved: 350 },
];

const mockWeeklyApplicationMetrics: ApplicationMetric[] = [
  { name: 'Week 1', application: 1200, approved: 700 },
  { name: 'Week 2', application: 1400, approved: 950 },
  { name: 'Week 3', application: 1100, approved: 800 },
  { name: 'Week 4', application: 1300, approved: 920 },
];

const mockMonthlyApplicationMetrics: ApplicationMetric[] = [
  { name: 'Jan', application: 4500, approved: 3200 },
  { name: 'Feb', application: 4200, approved: 3000 },
  { name: 'Mar', application: 4800, approved: 3600 },
  { name: 'Apr', application: 5000, approved: 3800 },
  { name: 'May', application: 5200, approved: 4000 },
  { name: 'Jun', application: 4900, approved: 3700 },
];

const mockApplicationTrend: TrendPoint[] = [
  { value: 10 },
  { value: 15 },
  { value: 13 },
  { value: 17 },
  { value: 20 },
  { value: 18 },
  { value: 22 },
  { value: 25 },
  { value: 20 },
  { value: 28 },
];

const mockApprovalTrend: TrendPoint[] = [
  { value: 5 },
  { value: 8 },
  { value: 10 },
  { value: 12 },
  { value: 15 },
  { value: 13 },
  { value: 18 },
  { value: 15 },
  { value: 17 },
  { value: 20 },
];

// Create the store
export const useDashboardStore = create<DashboardState>((set, get) => ({
  isLoading: false,
  error: null,
  totalApplications: 700,
  totalApproved: 500,
  applicationTrend: mockApplicationTrend,
  approvalTrend: mockApprovalTrend,
  cardMetrics: mockCardMetrics,
  applicationMetrics: mockDailyApplicationMetrics,
  timeframe: 'weekly',
  
  // Fetch dashboard data (simulated API call)
  // fetchDashboardData: async () => {
  //   set({ isLoading: true, error: null });
    
  //   try {
  //     // Simulate API delay
  //     await new Promise((resolve) => setTimeout(resolve, 1000));
      
  //     const timeframe = get().timeframe;
  //     let applicationMetrics;
      
  //     // Set appropriate metrics based on timeframe
  //     switch (timeframe) {
  //       case 'daily':
  //         applicationMetrics = mockDailyApplicationMetrics;
  //         break;
  //       case 'monthly':
  //         applicationMetrics = mockMonthlyApplicationMetrics;
  //         break;
  //       case 'weekly':
  //       default:
  //         applicationMetrics = mockWeeklyApplicationMetrics;
  //         break;
  //     }
      
  //     set({ 
  //       isLoading: false,
  //       applicationMetrics,
  //     });
  //   } catch (error) {
  //     set({ 
  //       isLoading: false, 
  //       error: 'Failed to fetch dashboard data. Please try again.' 
  //     });
  //   }
  // },
  // fetchDashboardData: async () => {
  //   set({ isLoading: true, error: null });
  
  //   try {
  //     const user = useAuthStore.getState().user;
  
  //     if (!user) {
  //       throw new Error('User not authenticated');
  //     }
  
  //     const dashboard = user.dashboard;
  //     const timeframe = get().timeframe;
  
  //     let applicationMetrics: ApplicationMetric[] = [];
  
  //     // You can later improve this mapping logic based on how `daily_summary` is structured
  //     if (timeframe === 'daily') {
  //       applicationMetrics = dashboard.daily_summary.map((entry: any) => ({
  //         name: entry.day || 'Day',
  //         application: entry.total_applications || 0,
  //         approved: entry.total_approved || 0,
  //       }));
  //     } else if (timeframe === 'monthly' || timeframe === 'weekly') {
  //       // Adjust if backend sends this summary too
  //       applicationMetrics = dashboard.bank_wise_summary.map((entry: any) => ({
  //         name: entry.bank_name || 'Bank',
  //         application: entry.total || 0,
  //         approved: entry.approved || 0,
  //       }));
  //     }
  
  //     set({
  //       isLoading: false,
  //       totalApplications: dashboard.total_summary.total_applications,
  //       totalApproved: dashboard.total_summary.total_approved,
  //       applicationMetrics,
  //       cardMetrics: dashboard.bank_wise_summary.map((entry: any) => ({
  //         name: entry.bank_name || 'Bank',
  //         total: entry.total || 0,
  //         approved: entry.approved || 0,
  //       })),
  //     });
  //   } catch (error) {
  //     set({
  //       isLoading: false,
  //       error: (error as Error).message || 'Failed to load dashboard data',
  //     });
  //   }
  // },
  fetchDashboardData: async () => {
  set({ isLoading: true, error: null });

  try {
    const user = useAuthStore.getState().user;

    if (!user) {
      throw new Error('User not authenticated');
    }

    const dashboard = user.dashboard || {};
    const timeframe = get().timeframe;

    // Use mock data if actual dashboard data is empty or not present
    const isMock = !dashboard.bank_wise_summary?.length && !dashboard.daily_summary?.length;

    let applicationMetrics: ApplicationMetric[] = [];

    if (timeframe === 'daily') {
      applicationMetrics = isMock
        ? mockDailyApplicationMetrics
        : dashboard.daily_summary.map((entry: any) => ({
            name: entry.day || 'Day',
            application: entry.total_applications || 0,
            approved: entry.total_approved || 0,
          }));
    } else if (timeframe === 'weekly') {
      applicationMetrics = isMock
        ? mockWeeklyApplicationMetrics
        : dashboard.daily_summary.map((entry: any) => ({
            name: entry.bank_name || 'Bank',
            application: entry.total_applications || 0,
            approved: entry.approved_applications || 0,
          }));
    } else if (timeframe === 'monthly') {
      applicationMetrics = isMock
        ? mockMonthlyApplicationMetrics
        : dashboard.current_month_summary.map((entry: any) => ({
            name: entry.bank_name || 'Bank',
            application: entry.total_applications || 0,
            approved: entry.approved_applications || 0,
          }));
    } else if (timeframe === '3months') {
      applicationMetrics = isMock
        ? mockMonthlyApplicationMetrics
        : dashboard.monthly_summary.map((entry: any) => ({
            name: entry.bank_name || 'Bank',
            application: entry.total_applications || 0,
            approved: entry.approved_applications || 0,
          }));
    }

    set({
      isLoading: false,
      totalApplications: isMock ? 700 : dashboard.total_summary?.total_applications ?? 0,
      totalApproved: isMock ? 500 : dashboard.total_summary?.total_approved ?? 0,
      applicationMetrics,
      cardMetrics: isMock
        ? mockCardMetrics
        : dashboard.bank_wise_summary.map((entry: any) => ({
            name: entry.bank_name || 'Bank',
            total: entry.total_applications || 0,
            approved: entry.approved_applications || 0,
          })),
    });
  } catch (error) {
    set({
      isLoading: false,
      error: (error as Error).message || 'Failed to load dashboard data',
    });
  }
},
  
  // Update timeframe and fetch new data
  setTimeframe: (timeframe) => {
    set({ timeframe });
    get().fetchDashboardData();
  },
}));