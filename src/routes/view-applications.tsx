import React, { useEffect, useState } from 'react';
import {
  Box, Heading, Table, Thead, Tbody, Tr, Th, Td, Button,
  <PERSON>lex, Badge, useColorModeValue, Spinner,
  Center
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { useApplicationStore } from '../store/applicationStore';

type Application = {
  id: string;
  date: string;
  mCode: string;
  name: string;
  mobile: string;
  score: number;
  status: string;
};

const ViewApplications: React.FC = () => {
  const navigate = useNavigate();
  const { applications, isLoading, fetchApplications } = useApplicationStore();
  const [isOfflineUpdating, setIsOfflineUpdating] = useState(false);

  const hoverBg = useColorModeValue('gray.50', 'gray.700');
  const tableBg = useColorModeValue('white', 'gray.800');

  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  const handleViewDetails = (id: string) => {
    navigate(`/applications/${id}`);
  };

  const handleOfflineUpdate = () => {
    setIsOfflineUpdating(true);
    setTimeout(() => setIsOfflineUpdating(false), 2000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Bureau check':
        return 'blue';
      case 'Card Offered':
        return 'green';
      case 'Application Started':
        return 'orange';
      default:
        return 'gray';
    }
  };

  return (
    <Box height="100%" display="flex" flexDirection="column">
      <Box
        bg={tableBg}
        borderRadius="2xl"
        p={6}
        boxShadow="md"
        height="100%"
        display="flex"
        flexDirection="column"
      >
        <Flex justifyContent="flex-end" mb={4}>
          <Button
            colorScheme="blue"
            isLoading={isOfflineUpdating}
            loadingText="Updating"
            borderRadius="lg"
            px={6}
            fontWeight="medium"
            onClick={handleOfflineUpdate}
          >
            Offline Update
          </Button>
        </Flex>

        {isLoading ? (
          <Flex justifyContent="center" py={10}>
            <Spinner size="xl" />
          </Flex>
        ) : (
          <Box
            flex="1"
            overflowX="auto"
            display="flex"
            flexDirection="column"
            minHeight="0"
          >
            <Table variant="unstyled" size="md" height="100%">
              <Thead position="sticky" top="0" zIndex="1" bg={tableBg}>
                <Tr>
                  {['Date', 'M Code', 'Name', 'Mobile', 'Score', 'Status', 'Action'].map((header) => (
                    <Th key={header}>
                      {header}
                    </Th>
                  ))}
                </Tr>
              </Thead>
            </Table>
            <Box
              flex="1"
              overflowY="auto"
              overflowX="auto"
              sx={{
                '&::-webkit-scrollbar': {
                  width: '6px',
                  height: '6px',
                  borderRadius: '8px',
                  backgroundColor: 'rgba(0, 0, 0, 0.05)',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                  borderRadius: '8px',
                },
              }}
            >
              <Table variant="unstyled" size="md">
                <Tbody>
                  {applications.map((application) => (
                    <Tr key={application.id}>
                      <Td>{application.date}</Td>
                      <Td>{application.mCode}</Td>
                      <Td>{application.name}</Td>
                      <Td>{application.mobile}</Td>
                      <Td>{application.score}</Td>
                      <Td>
                        <Badge
                          colorScheme={getStatusColor(application.status)}
                          px={2}
                          borderRadius="md"
                          maxW="100px"
                          whiteSpace="nowrap"
                          overflow="hidden"
                          textOverflow="ellipsis"
                        >
                          {application.status}
                        </Badge>
                      </Td>
                      <Td>
                        <Button
                          size="sm"
                          variant="outline"
                          borderRadius="md"
                          px={4}
                          fontWeight="medium"
                          onClick={() => handleViewDetails(application.id)}
                        >
                          Details
                        </Button>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            </Box>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default ViewApplications;
