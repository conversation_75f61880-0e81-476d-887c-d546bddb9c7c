// src/routes/rules/view-templates.tsx
import React from 'react';
import {
    Box,
    Table,
    Thead,
    Tbody,
    Tr,
    Th,
    Td,
    Button,
    TableContainer,
    Text,
    Card,
    CardBody,
    useColorModeValue,
} from '@chakra-ui/react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { FiPlus } from 'react-icons/fi';
import { useTemplateStore } from '../../store/templateStore';

const ViewTemplates = () => {
    const { templates, isLoading } = useTemplateStore();
    const navigate = useNavigate();
    const bgColor = useColorModeValue('white', 'gray.800');
    const borderColor = useColorModeValue('gray.100', 'gray.700');

    const handleCreateTemplate = () => {
        navigate('/rules/create');
    };

    return (
        <Box>
            <Card
                bg={bgColor}
                boxShadow="sm"
                borderRadius="2xl"
                borderWidth="1px"
                borderColor={borderColor}
                p={5}
            >
                <CardBody p={0}>
                    <Box display="flex" justifyContent="flex-end" mb={6}>
                    <Button
                        colorScheme="brand"
                        onClick={handleCreateTemplate}
                        leftIcon={<FiPlus />}
                    >
                        Create Template
                    </Button>
                    </Box>
                    <TableContainer>
                        <Table variant="unstyled" size="md">
                            <Thead>
                                <Tr>
                                    <Th>Date</Th>
                                    <Th>Template Name</Th>
                                    <Th>Card Linked</Th>
                                    <Th>Action</Th>
                                </Tr>
                            </Thead>
                            <Tbody>
                                {isLoading ? (
                                    <Tr>
                                        <Td colSpan={4} textAlign="center" py={8}>
                                            Loading templates...
                                        </Td>
                                    </Tr>
                                ) : templates.length === 0 ? (
                                    <Tr>
                                        <Td colSpan={4} textAlign="center" py={10}>
                                            <Text fontSize="md" color="gray.500" mb={4}>
                                                No templates found. Create your first template!
                                            </Text>
                                            <Button
                                                colorScheme="brand"
                                                size="sm"
                                                leftIcon={<FiPlus />}
                                                onClick={handleCreateTemplate}
                                            >
                                                Create Template
                                            </Button>
                                        </Td>
                                    </Tr>
                                ) : (
                                    templates.map((template) => (
                                        <Tr key={template.id} _hover={{ bg: 'gray.50' }}>
                                            <Td>{template.date}</Td>
                                            <Td fontWeight="medium">{template.name}</Td>
                                            <Td>{template.cardLinked}</Td>
                                            <Td>
                                                <Button
                                                    as={RouterLink}
                                                    to={`/rules/edit/${template.id}`}
                                                    variant="outline"
                                                    colorScheme="brand"
                                                    size="sm"
                                                >
                                                    Details
                                                </Button>
                                            </Td>
                                        </Tr>
                                    ))
                                )}
                            </Tbody>
                        </Table>
                    </TableContainer>
                </CardBody>
            </Card>
        </Box>
    );
};

export default ViewTemplates;