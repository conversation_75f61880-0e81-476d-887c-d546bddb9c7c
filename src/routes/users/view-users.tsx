// src/routes/users/view-users.tsx
import { useState, useEffect } from 'react';
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  Heading,
  Flex,
  Text,
  Badge,
  Spinner,
  useToast,
  useDisclosure,
  useColorModeValue
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { useUserStore } from '../../store/userStore';
import { useAuthStore } from '../../store/authStore';

const ViewUsers = () => {
  const { users, isLoading, error, fetchUsers } = useUserStore();
  const navigate = useNavigate();
  const toast = useToast();
  const { user } = useAuthStore();

  const canEditUsers = user?.permissions?.find(p => p.module_name === 'users')?.can_edit;

  const tableBg = useColorModeValue('white', 'gray.800');

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  useEffect(() => {
    if (error) {
      toast({
        title: 'Error fetching users',
        description: error,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  }, [error, toast]);

  const handleViewUser = (userId: string) => {
    navigate(`/users/${userId}`);
  };

  const handleCreateUser = () => {
    navigate('/users/create');
  };

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return <Badge colorScheme="green">Approved</Badge>;
      case 'pending':
        return <Badge colorScheme="red">Pending</Badge>;
      default:
        return <Badge colorScheme="gray">{status}</Badge>;
    }
  };

  return (
    <Box>
      <Box
        bg={tableBg}
        borderRadius="2xl"
        p={6}
        boxShadow="md"
      >
        <Flex justifyContent="flex-end" mb={6}>
        {canEditUsers && (
        <Button colorScheme="blue" onClick={handleCreateUser}>
          Create
        </Button>
         )}
      </Flex>

      {isLoading ? (
        <Flex justifyContent="center" my={10}>
          <Spinner size="xl" />
        </Flex>
      ) : (
        <Box overflowX="auto">
          <Table variant="unstyled">
            <Thead>
              <Tr>
                <Th>Employee Code</Th>
                <Th>Name</Th>
                <Th>Mobile</Th>
                <Th>Status</Th>
                <Th>Action</Th>
              </Tr>
            </Thead>
            <Tbody>
              {users && users.length > 0 ? (
                users.map((user) => (
                  <Tr key={user.id}>
                    <Td>{user.employeeCode}</Td>
                    <Td>{user.name}</Td>
                    <Td>{user.mobile}</Td>
                    <Td>{getStatusBadge(user.status)}</Td>
                    <Td>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleViewUser(user.id)}
                      >
                        View
                      </Button>
                    </Td>
                  </Tr>
                ))
              ) : (
                <Tr>
                  <Td colSpan={5}>
                    <Text textAlign="center">No users found</Text>
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </Box>
      )}
      </Box>
      
    </Box>
  );
};

export default ViewUsers;